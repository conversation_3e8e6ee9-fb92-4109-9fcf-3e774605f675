import { storeToRefs } from "pinia"
import { ref, watch, nextTick } from "vue"
import { commonStore } from "~/stores/commonStore"
import { getKlinesApi } from '~/api/order'


export default function useDatafeedAction(info, options = {}) {
  const { 
    enableProfessionalOptimization = false 
  } = options
  
  // 暴露周期切换状态给外部组件
  let isResolutionChanging = ref(false)
  
  // 专业版历史数据加载状态管理 - 优化版本
  const historicalDataStatus = enableProfessionalOptimization ? new Map() : null
  const HISTORICAL_DATA_TIMEOUT = 3000 // 减少到3秒超时，提升响应速度
  
  const dataCache = new Map()
  const requestCache = new Map()
  const activeRequests = new Map()
  const REQUEST_TIMEOUT = 30 * 1000
  
  // 新增：请求合并机制
  const pendingRequests = new Map() // 存储待处理的请求
  const requestMergeWindow = 500 // 500ms内的相同请求会被合并
  
  // 优化：专业版使用更短的缓存时间，确保数据新鲜度和响应速度
  const CACHE_DURATION = enableProfessionalOptimization 
    ? 5 * 60 * 1000  // 专业版缓存5分钟，提升响应速度
    : 10 * 60 * 1000
  const pairInfo = info
  const store = commonStore()
  const interval = ref('')
  const pair = ref('')
  const preObj = ref({})
  // 新增：专门用于历史数据分页的时间戳存储
  const historyPaginationTime = ref<number | null>(null)
  const { klineList, klineTicker, ticker } = storeToRefs(store)
  const resolutionMap: any = {
    1: '1m',
    // 3: '3m',
    5: '5m',
    15: '15m',
    30: '30m',
    60: '1h',
    120: '2h',
    240: '4h',
    360: '6h',
    480: '8h',
    720: '12h',
    '1D': '1d',
    // '3D': '3day',
    '1W': '1w',
    '1M': '1M'
  }

  const resolutionReMap: any = {
    'line': 1,
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '30m': 30,
    '1h': 60,
    '2h': 120,
    '4h': 240,
    '6h': 360,
    '8h': 480,
    '12h': 720,
    '1d': '1D',
    '1w': '1W',
    '1M': '1M'
  }
  // 专业版订阅管理系统 - 基于第二版备份的高效架构
  const subscriptions = new Map() // subscriberUID -> subscription info
  const symbolSubscriptions = new Map() // symbol_resolution -> Set of subscriberUIDs
  const lastBarTime = new Map() // symbolKey -> timestamp
  const lastBarData = new Map() // symbolKey -> bar data
  
  // 兼容旧系统
  const subMap: any = {}
  let rafId: number | null = null
  

  function formatSymbol (symbol: string) {
    return symbol.toUpperCase()
  }

  const safeNumber = (value: any, fallback: number = 0): number => {
    const num = Number(value)
    return isNaN(num) || !isFinite(num) ? fallback : Math.abs(num)
  }
  
  // 时间戳格式转换：确保返回毫秒级时间戳
  const ensureMilliseconds = (timestamp: number): number => {
    // 如果是10位数，认为是秒级时间戳，转换为毫秒
    if (timestamp > 0 && timestamp < 10000000000) {
      return timestamp * 1000
    }
    return timestamp
  }
  
  // 验证时间戳是否在合理范围内
  const isValidTimestamp = (timestamp: number): boolean => {
    const now = Date.now()
    const minTime = new Date('2020-01-01').getTime() // 最早允许2020年
    // 对于历史数据请求，不应该接受未来的时间戳
    // 只允许到当前时间，不再允许未来时间
    const maxTime = now
    
    return timestamp >= minTime && timestamp <= maxTime
  }

  // 专业版数据构建函数 - 基于第二版备份
  function buildBarData(klineTickerData: any, tickerLast: any, symbolKey: string) {
    const currentClose = safeNumber(tickerLast)
    
    // 优先使用完整的K线数据
    if (klineTickerData.time && klineTickerData.open !== undefined) {
      const barTime = Number(klineTickerData.time)
      
      // 构建完整K线数据
      const barData = {
        time: barTime,
        open: safeNumber(klineTickerData.open),
        high: safeNumber(klineTickerData.high),
        low: safeNumber(klineTickerData.low),
        close: currentClose,
        volume: safeNumber(klineTickerData.volume)
      }

      // 更新缓存
      lastBarTime.set(symbolKey, barTime)
      lastBarData.set(symbolKey, barData)
      
      return barData
    }

    // 如果只有价格数据，基于最后一个bar更新
    const lastBar = lastBarData.get(symbolKey)
    if (lastBar) {
      const updatedBar = {
        ...lastBar,
        close: currentClose,
        high: Math.max(lastBar.high, currentClose),
        low: Math.min(lastBar.low, currentClose),
        time: Date.now() // 使用当前时间
      }
      
      lastBarData.set(symbolKey, updatedBar)
      return updatedBar
    }

    return null
  }

  // 专业版数据推送函数 - 基于第二版备份的高效机制
  function pushRealtimeDataToSubscribers(tickerData: any, klineTickerData: any) {
    if (!tickerData || !klineTickerData || !klineTickerData.currentPair || !klineTickerData.currentPeriod) {
      return
    }

    const currentSymbol = formatSymbol(klineTickerData.currentPair)
    const currentInterval = klineTickerData.currentPeriod
    const symbolKey = `${currentSymbol}_${currentInterval}`

    // 获取对应的订阅者
    const subscriberIds = symbolSubscriptions.get(symbolKey)
    if (!subscriberIds || subscriberIds.size === 0) {
      return
    }

    const last = (tickerData[currentSymbol] || {}).last
    if (!last) {
      return
    }

    // 构建K线数据
    const barData = buildBarData(klineTickerData, last, symbolKey)
    if (!barData) {
      return
    }

    // 推送完整数据到所有匹配的订阅者
    subscriberIds.forEach(subscriberUID => {
      const subscription = subscriptions.get(subscriberUID)
      if (subscription && subscription.active) {
        if (formatSymbol(subscription.symbol) === currentSymbol &&
          subscription.interval === currentInterval) {
          try {
            subscription.callback({ ...barData })
          } catch (error) {
            console.warn('[专业版] 推送数据失败:', error)
          }
        }
      }
    })
  }
  
  // 专业版历史数据状态管理函数
  const getHistoricalDataKey = (symbol: string, resolution: string) => {
    return `${symbol}-${resolutionMap[resolution]}`
  }
  
  const setHistoricalDataLoading = (symbol: string, resolution: string) => {
    if (!enableProfessionalOptimization || !historicalDataStatus) return
    const key = getHistoricalDataKey(symbol, resolution)
    historicalDataStatus.set(key, { 
      loading: true, 
      ready: false, 
      timestamp: Date.now() 
    })
  }
  
  const setHistoricalDataReady = (symbol: string, resolution: string) => {
    if (!enableProfessionalOptimization || !historicalDataStatus) return
    const key = getHistoricalDataKey(symbol, resolution)
    historicalDataStatus.set(key, { 
      loading: false, 
      ready: true, 
      timestamp: Date.now() 
    })
  }
  
  const isHistoricalDataReady = (symbol: string, resolution: string) => {
    if (!enableProfessionalOptimization || !historicalDataStatus) return true
    const key = getHistoricalDataKey(symbol, resolution)
    const status = historicalDataStatus.get(key)
    
    // 优化1：大幅缩短超时时间，从10秒减少到3秒
    if (status && status.loading && (Date.now() - status.timestamp > HISTORICAL_DATA_TIMEOUT)) {
      setHistoricalDataReady(symbol, resolution)
      return true
    }
    
    // 优化2：如果没有状态记录，直接返回true（允许实时数据流动）
    if (!status) {
      return true
    }
    
    // 优化3：对于基础周期（1m, 5m, 15m），更快地认为就绪
    const quickReadyResolutions = ['1m', '5m', '15m']
    if (quickReadyResolutions.includes(resolutionMap[resolution]) && status.loading) {
      const quickTimeout = 1000 // 1秒快速超时
      if (Date.now() - status.timestamp > quickTimeout) {
        setHistoricalDataReady(symbol, resolution)
        return true
      }
    }
    
    return status ? status.ready : true // 默认返回true，减少阻塞
  }

  const getConsistentTime = (serverTime: any, baseTime?: number): number => {
    if (serverTime && Number(serverTime) > 0) {
      return Number(serverTime)
    }
    return baseTime || Date.now()
  }
  let key = ''
  async function handleMonthlyData(symbolInfo: any, resolution: any, periodParams: any, onHistoryCallback: any, onErrorCallback: any) {
    const { firstDataRequest } = periodParams

    if (firstDataRequest && resolution === '1M') {
      const hasValidCache = klineList.value.length > 0 && 
                           klineTicker.value.currentPeriod === '1M' &&
                           klineTicker.value.currentPair === symbolInfo.fullName
      
      if (hasValidCache) {
        preObj.value = klineList.value[klineList.value.length - 1]
        onHistoryCallback(klineList.value, {noData: false})
        return true
      }
    }
    
    return false
  }

  async function getBars(
    symbolInfo: any,
    resolution: any,
    periodParams: any,
    onHistoryCallback: any,
    onErrorCallback: any) {
    pair.value = symbolInfo.fullName
    interval.value = resolutionMap[resolution]
    key = `${symbolInfo.fullName}-${resolution}`
    const { from, to, firstDataRequest, countBack } = periodParams;  // `from` 和 `to` 用来确定需要获取的时间范围

    const currentResolution = resolutionMap[resolution]
    const isResolutionChange = firstDataRequest && interval.value && interval.value !== currentResolution
    
    if (isResolutionChange && !enableProfessionalOptimization) {
      const symbolPrefix = `${symbolInfo.fullName}-`
      const newResolutionPrefix = `${symbolInfo.fullName}-${currentResolution}-`
      
      const keysToDelete = []
      dataCache.forEach((value, key) => {
        if (key.startsWith(symbolPrefix) && !key.startsWith(newResolutionPrefix)) {
          keysToDelete.push(key)
        }
      })
      
      if (keysToDelete.length > 0) {
        keysToDelete.forEach(key => dataCache.delete(key))
      }
      
      const requestKeysToDelete = []
      requestCache.forEach((timestamp, key) => {
        if (key.startsWith(symbolPrefix) && !key.includes(`-${currentResolution}-`)) {
          requestKeysToDelete.push(key)
        }
      })
      
      if (requestKeysToDelete.length > 0) {
        requestKeysToDelete.forEach(key => requestCache.delete(key))
      }
    }

    if (resolution === '1M') {
      const handled = await handleMonthlyData(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback)
      if (handled) {
        return
      }
    }

    if (!interval.value || isResolutionChange) {
      interval.value = currentResolution
    }
    
    fetchHistoricalData(symbolInfo.fullName, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback);
  }
  const forceRefresh = ref(false)

  const clearCache = (selective = false) => {
    if (selective) {
      dataCache.clear()
      lastCompleteBar.value = {}
      cleanupExpiredRequests()
      // 清理历史分页时间戳
      historyPaginationTime.value = null
    } else {
      cancelAllActiveRequests()
      dataCache.clear()
      lastCompleteBar.value = {}
      lastPriceUpdates.clear()
      lastBarTimes.clear()
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
      klineList.value = []
      klineTicker.value = {}
      // 清理历史分页时间戳
      historyPaginationTime.value = null
      
      // 专业版：清理历史数据状态
      if (enableProfessionalOptimization && historicalDataStatus) {
        historicalDataStatus.clear()
      }
    }
  }

  const setForceRefresh = (force: boolean) => {
    forceRefresh.value = force
    if (force) {
      cleanupExpiredRequests()
    }
  }
  
  const cleanupExpiredRequests = () => {
    const now = Date.now()
    const expiredKeys = []
    
    requestCache.forEach((timestamp, key) => {
      if (now - timestamp > REQUEST_TIMEOUT) {
        expiredKeys.push(key)
      }
    })
    expiredKeys.forEach(key => {
      requestCache.delete(key)
      if (activeRequests.has(key)) {
        const controller = activeRequests.get(key)
        if (controller && !controller.signal.aborted) {
          controller.abort('Request timeout')
        }
        activeRequests.delete(key)
      }
    })
  }

  const cancelAllActiveRequests = () => {
    activeRequests.forEach((controller, key) => {
      if (controller && !controller.signal.aborted) {
        controller.abort('Component cleanup')
      }
    })
    activeRequests.clear()
    requestCache.clear()
  }

  setInterval(cleanupExpiredRequests, REQUEST_TIMEOUT / 2)

  const callStack = new Map<string, number>()
  const MAX_RECURSIVE_CALLS = 8
  
  async function fetchHistoricalData(symbol: string, resolution: string, from: number, to: number, firstDataRequest: Boolean, countBack: number, onHistoryCallback: any, onErrorCallback: any) {
    const callKey = `${symbol}-${resolutionMap[resolution]}-${firstDataRequest}`
    const currentCalls = callStack.get(callKey) || 0
    if (currentCalls >= MAX_RECURSIVE_CALLS) {
      onErrorCallback('数据加载失败，请重试')
      return
    }
    callStack.set(callKey, currentCalls + 1)
    
    try {
      await fetchHistoricalDataInternal(symbol, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback)
    } finally {
      setTimeout(() => {
        callStack.delete(callKey)
      }, 1000)
    }
  }
  
  async function fetchHistoricalDataInternal(symbol: string, resolution: string, from: number, to: number, firstDataRequest: Boolean, countBack: number, onHistoryCallback: any, onErrorCallback: any) {
    cleanupExpiredRequests()
    
    // 专业版：设置历史数据加载状态 - 优化并行加载
    if (firstDataRequest) {
      setHistoricalDataLoading(symbol, resolution)
      
      // 优化：立即标记为可用，启用并行数据流
      // 在1秒后自动设置为就绪，不等待历史数据完全加载
      setTimeout(() => {
        setHistoricalDataReady(symbol, resolution)
      }, 1000) // 1秒后自动启用实时数据流
      
      // 初始化时清理状态，确保从干净状态开始
      preObj.value = {}
      
      // 清理该symbol的所有缓存
      const symbolPrefix = `${symbol}-`
      const keysToDelete = []
      dataCache.forEach((value, key) => {
        if (key.startsWith(symbolPrefix)) {
          keysToDelete.push(key)
        }
      })
      keysToDelete.forEach(key => dataCache.delete(key))
    }
    
    // 优化：对时间参数进行归一化处理，减少因微小时间差异导致的重复请求
    const timeWindow = firstDataRequest ? 60000 : 5000 // 初始请求使用60秒窗口，其他使用5秒窗口
    const normalizedFrom = Math.floor(from / timeWindow) * timeWindow
    const normalizedTo = Math.floor(to / timeWindow) * timeWindow
    
    // 统一使用基于请求参数的缓存key，避免时间范围不连续问题
    const requestKey = enableProfessionalOptimization && firstDataRequest
      ? `${symbol}-${resolutionMap[resolution]}-init-${countBack}` // 初始请求使用简化的key
      : `${symbol}-${resolutionMap[resolution]}-${normalizedFrom}-${normalizedTo}-${countBack}`
    
    const cacheKey = enableProfessionalOptimization 
      ? `${symbol}-${resolutionMap[resolution]}-${Math.floor(from / 86400000)}-${firstDataRequest ? 'first' : 'hist'}`
      : `${symbol}-${resolutionMap[resolution]}-${firstDataRequest ? 'initial' : Math.floor(from / (24 * 60 * 60 * 1000))}`
    
    if (requestCache.has(requestKey)) {
      if (enableProfessionalOptimization) {
        const requestTime = requestCache.get(requestKey)
        // 初始化请求使用更长的防重复时间窗口
        const dedupWindow = firstDataRequest ? 30000 : 10000 // 初始请求30秒，其他10秒
        if (Date.now() - requestTime >= dedupWindow) {
          requestCache.delete(requestKey)
        } else {
          return
        }
      } else {
        return
      }
    }
    
    const cachedData = dataCache.get(cacheKey)
    const isMonthlyResolution = resolution === '1M'
    
    // 专业版优化：使用更短的缓存时间，优先保证数据新鲜度
    const cacheTimeout = enableProfessionalOptimization
      ? (firstDataRequest ? 10 * 60 * 1000 : // 初始请求缓存10分钟（减少等待）
         isMonthlyResolution ? 5 * 60 * 1000 : CACHE_DURATION)
      : (isMonthlyResolution ? 3 * 60 * 1000 : CACHE_DURATION)
    
    const shouldUseCache = cachedData && 
                          (Date.now() - cachedData.timestamp < cacheTimeout) && 
                          !forceRefresh.value &&
                          cachedData.data.length > 0
    
    
    if (shouldUseCache) {
      const noMoreData = cachedData.data.length < (firstDataRequest ? 30 : 10)
      if (forceRefresh.value) {
        forceRefresh.value = false
      }
      try {
        onHistoryCallback(cachedData.data, { noData: noMoreData })
        return
      } catch (error) {
        dataCache.delete(cacheKey)
      }
    }

    // 新增：请求合并逻辑
    const mergeKey = `${symbol}-${resolutionMap[resolution]}-${firstDataRequest}`
    
    // 检查是否有相同的请求正在处理
    if (pendingRequests.has(mergeKey)) {
      const pendingRequest = pendingRequests.get(mergeKey)
      // 如果请求还在合并窗口内，直接返回该请求的结果
      if (Date.now() - pendingRequest.timestamp < requestMergeWindow) {
        try {
          // 等待请求完成
          await pendingRequest.promise
          // 使用存储的处理后数据
          if (pendingRequest.data && pendingRequest.meta) {
            onHistoryCallback(pendingRequest.data, pendingRequest.meta)
            return
          }
        } catch (error) {
          // 如果合并的请求失败了，继续执行当前请求
        }
      } else {
        // 超出合并窗口，删除旧的待处理请求
        pendingRequests.delete(mergeKey)
      }
    }

    try {
      const controller = new AbortController()
      requestCache.set(requestKey, Date.now())
      activeRequests.set(requestKey, controller)
      
      const now = Date.now()
      const requestLimit = firstDataRequest ? 
        (isMonthlyResolution ? Math.min(countBack, 300) : Math.min(countBack, 300)) :
        (isMonthlyResolution ? Math.min(countBack, 300) : Math.min(countBack, 300))

      let beforeTime
      if (firstDataRequest) {
        beforeTime = now
        // 首次请求时重置分页时间戳
        historyPaginationTime.value = null
      } else {
        // 分页请求：优先使用独立的历史分页时间戳
        if (historyPaginationTime.value && historyPaginationTime.value > 0 && isValidTimestamp(historyPaginationTime.value)) {
          beforeTime = historyPaginationTime.value
        } else {
          // 兼容旧逻辑，但需要验证时间戳有效性
          const preObjTime = preObj.value?.time
          if (preObjTime && preObjTime > 0) {
            const millisTime = ensureMilliseconds(preObjTime)
            if (isValidTimestamp(millisTime)) {
              beforeTime = millisTime
            } else {
              // 验证 to 参数是否有效
              const toMillis = ensureMilliseconds(to)
              if (isValidTimestamp(toMillis)) {
                beforeTime = toMillis
              } else {
                // 如果 to 也是未来时间，使用当前时间减去一小时作为安全的历史时间
                beforeTime = Date.now() - 60 * 60 * 1000
              }
            }
          } else {
            // 验证 to 参数是否有效
            const toMillis = ensureMilliseconds(to)
            if (isValidTimestamp(toMillis)) {
              beforeTime = toMillis
            } else {
              // 如果 to 是未来时间，使用当前时间减去一小时作为安全的历史时间
              beforeTime = Date.now() - 60 * 60 * 1000
            }
          }
        }
      }

      if (controller.signal.aborted) {
        return
      }

      // 创建请求Promise并存储到pendingRequests
      const requestPromise = getKlinesApi({
        symbol: formatSymbol(symbol),
        market: formatSymbol(symbol).includes('_SWAP') ? 'lpc' : 'spot',
        time_frame: resolutionMap[resolution],
        before: beforeTime,
        limit: requestLimit,
        origin: 1,
      })
      
      // 存储待处理请求
      pendingRequests.set(mergeKey, {
        promise: requestPromise,
        timestamp: Date.now()
      })
      
      const { data } = await requestPromise
      
      if (controller.signal.aborted) {
        return
      }

      if (data) {
        // 如果没有 data.e 或 data.e 为空数组，说明没有更多数据
        if (!data.e || data.e.length === 0) {
          onHistoryCallback([], { noData: true })
          return
        }
        
        let formattedData = data.e.map(item => {
          const time = ensureMilliseconds(Number(item[0]))
          const open = Math.abs(Number(item[1]))
          const high = Math.abs(Number(item[2]))
          const low = Math.abs(Number(item[3]))
          const close = Math.abs(Number(item[4]))
          const volume = Math.abs(Number(item[5]))
          return { time, open, high, low, close, volume }
        })
        
        // 严格的时间顺序控制：去重、排序、验证
        const uniqueData = new Map()
        let invalidCount = 0
        
        formattedData.forEach(item => {
          // 使用独立的验证函数检查时间戳
          if (isValidTimestamp(item.time)) {
            uniqueData.set(item.time, item)
          } else {
            invalidCount++
          }
        })
        // 如果所有数据都被过滤掉了，说明API返回的都是无效数据
        if (uniqueData.size === 0 && data.e.length > 0) {
          // 通知 TradingView 没有更多历史数据
          onHistoryCallback([], { noData: true })
          return
        }
        
        // 严格按时间升序排列
        formattedData = Array.from(uniqueData.values()).sort((a, b) => a.time - b.time)
        
        // 严格的时间连续性验证
        const validatedData = []
        let lastTime = 0
        for (const item of formattedData) {
          // 确保时间严格递增，没有重复或倒序
          if (item.time > lastTime) {
            validatedData.push(item)
            lastTime = item.time
          }
        }
        formattedData = validatedData
        
        // 最终验证：确保数据按时间严格升序
        if (formattedData.length > 1) {
          for (let i = 1; i < formattedData.length; i++) {
            if (formattedData[i].time <= formattedData[i-1].time) {
              formattedData.splice(i, 1)
              i-- // 调整索引
            }
          }
        }

        if (formattedData.length > 0) {
          // 对于分页加载历史数据，应该使用最早（第一个）数据的时间戳
          // 这样下次请求会获取更早的历史数据
          if (!firstDataRequest) {
            // 分页请求：查找第一个有效的历史时间戳
            let validEarliestTime = null
            
            // 从最早的数据开始查找有效的时间戳
            for (const data of formattedData) {
              if (isValidTimestamp(data.time)) {
                validEarliestTime = data.time
                break
              }
            }
            
            if (validEarliestTime) {
              // 确保新的分页时间戳比当前的更早（时间递减）
              if (!historyPaginationTime.value || validEarliestTime < historyPaginationTime.value) {
                preObj.value = formattedData.find(item => item.time === validEarliestTime)
                historyPaginationTime.value = validEarliestTime
              }
            }
          } else {
            // 首次请求：保持使用最新的时间戳（用于实时更新）
            preObj.value = formattedData[formattedData.length - 1]
            
            // 首次请求时，初始化历史分页时间戳为最早的有效数据
            if (!historyPaginationTime.value && formattedData.length > 0) {
              // 查找第一个有效的时间戳作为初始分页时间
              for (const data of formattedData) {
                if (isValidTimestamp(data.time)) {
                  historyPaginationTime.value = data.time
                  break
                }
              }
            }
          }
          
          if (firstDataRequest && formattedData.length >= 10 && formattedData.length > 0) {
            dataCache.set(cacheKey, {
              data: formattedData,
              timestamp: now
            })
          }

          if (isMonthlyResolution && firstDataRequest && formattedData.length > 0) {
            klineList.value = formattedData
            klineTicker.value = {
              ...formattedData[formattedData.length - 1],
              currentPair: symbol,
              currentPeriod: '1M'
            }
          }
        }

        const noMoreData = formattedData.length < requestLimit
        
        // 专业版：成功加载后设置历史数据就绪状态
        if (firstDataRequest) {
          setHistoricalDataReady(symbol, resolution)
        }
        
        // 更新pendingRequests中的结果，供后续合并请求使用
        if (pendingRequests.has(mergeKey)) {
          const pending = pendingRequests.get(mergeKey)
          pending.data = formattedData
          pending.meta = { noData: noMoreData }
        }
        
        onHistoryCallback(formattedData, { noData: noMoreData })
      } else {
        // 专业版：加载失败时重置状态
        if (firstDataRequest) {
          setHistoricalDataReady(symbol, resolution)
        }
        onErrorCallback('No data received from API')
      }
    } catch (error) {
      if (error.name === 'AbortError' || error.message?.includes('aborted') || error.message?.includes('timeout')) {
        return
      }
      // 专业版：错误时重置状态
      if (firstDataRequest) {
        setHistoricalDataReady(symbol, resolution)
      }
      onErrorCallback(error)
    } finally {
      try {
        requestCache.delete(requestKey)
        activeRequests.delete(requestKey)
        // 延迟清理pendingRequests，让后续请求有机会使用缓存的结果
        setTimeout(() => {
          pendingRequests.delete(mergeKey)
        }, requestMergeWindow)
      } catch (cleanupError) {
      }
    }
  }
  let lastCompleteBar = ref({})

  let monthlySubscriptionCache = {
    key: null,
    subscription: null,
    pair: null
  }

  function handleMonthlyRealtimeUpdate(val1: any, val2: any) {
    // 专业版：为月度数据提供ticker快速通道
    if (enableProfessionalOptimization) {
      if (interval.value !== '1M' || isResolutionChanging.value) {
        return false
      }
      
      const hasTickerUpdate = val1 && val1[pair.value] && val1[pair.value].last
      const hasCompleteKlineData = val2 && val2.currentPair === pair.value && val2.currentPeriod === '1M'
      
      // 如果有完整K线数据，执行严格检查
      if (hasCompleteKlineData && !isHistoricalDataReady(pair.value, '1M')) {
        return false
      }
      
      // 如果只有ticker数据，提供快速通道
      if (hasTickerUpdate && !hasCompleteKlineData) {
        const monthlyKey = `${pair.value}_#_1M`
        const last = val1[pair.value].last
        
        let monthlySubscription = null
        
        if (monthlySubscriptionCache.pair === pair.value &&
            monthlySubscriptionCache.key &&
            subMap[monthlySubscriptionCache.key]) {
          monthlySubscription = monthlySubscriptionCache.subscription
        } else if (subMap[monthlyKey]) {
          monthlySubscription = subMap[monthlyKey]
        }
        
        if (monthlySubscription && last) {
          const resultVal = {
            time: Date.now(),
            close: safeNumber(last),
            open: lastCompleteBar.value[monthlyKey]?.open || safeNumber(last),
            high: Math.max(lastCompleteBar.value[monthlyKey]?.high || safeNumber(last), safeNumber(last)),
            low: Math.min(lastCompleteBar.value[monthlyKey]?.low || safeNumber(last), safeNumber(last)),
            volume: lastCompleteBar.value[monthlyKey]?.volume || 0
          }
          
          const currentTime = resultVal.time
          const lastTime = lastBarTimes.get(monthlyKey) || 0
          if (currentTime > lastTime) {
            lastBarTimes.set(monthlyKey, currentTime)
            monthlySubscription.listen(resultVal)
          }
          return true
        }
        return false
      }
    }
    // 基本版：保持原有逻辑
    else if (interval.value !== '1M' || isResolutionChanging.value) {
      return false
    }

    const monthlyKey = `${pair.value}_#_1M`
    const last = (val1[pair.value] || {}).last

    let monthlySubscription = null
    let subscriptionKey = null

    if (monthlySubscriptionCache.pair === pair.value &&
        monthlySubscriptionCache.key &&
        subMap[monthlySubscriptionCache.key]) {
      monthlySubscription = monthlySubscriptionCache.subscription
      subscriptionKey = monthlySubscriptionCache.key
    } else {
      if (subMap[monthlyKey]) {
        monthlySubscription = subMap[monthlyKey]
        subscriptionKey = monthlyKey
      } else {
        Object.keys(subMap).forEach(key => {
          const sub = subMap[key]
          if (sub && sub.symbol && formatSymbol(sub.symbol) === pair.value &&
              sub.resolution && resolutionMap[sub.resolution] === '1M') {
            monthlySubscription = sub
            subscriptionKey = key
          }
        })
      }

      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: monthlySubscription,
        pair: pair.value
      }
    }

    if (monthlySubscription && last && val2 && val2.currentPair &&
        formatSymbol(monthlySubscription.symbol) === val2.currentPair &&
        val2.currentPeriod === '1M' && val2.time && val2.open !== undefined) {

      const resultVal = {
        time: getConsistentTime(val2.time),
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        close: safeNumber(last),
        volume: safeNumber(val2.volume)
      }

      const monthlyStateKey = `${pair.value}_#_1M`
      lastCompleteBar.value[monthlyStateKey] = {
        time: resultVal.time,
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        volume: safeNumber(val2.volume)
      }

      const currentTime = resultVal.time || Date.now()
      const lastTime = lastBarTimes.get(monthlyStateKey) || 0
      // 严格时间验证：确保月度数据时间递增
      if (currentTime > lastTime) {
        lastBarTimes.set(monthlyStateKey, currentTime)
        monthlySubscription.listen(resultVal)
      }
      return true
    }

    return false
  }

  watch([ticker, klineTicker], ([val1, val2]) => {
    // 专业版：检查历史数据加载状态和周期切换状态
    if (enableProfessionalOptimization) {
      if (isResolutionChanging.value) {
        return
      }
      
      // 优化：扩大ticker快速通道，优先保证价格传递
      const hasTickerUpdate = val1 && val1[pair.value] && val1[pair.value].last
      const hasCompleteKlineData = val2 && val2.currentPair === pair.value && val2.currentPeriod
      
      // 使用实际数据中的周期，而不是全局的interval.value
      const actualPeriod = val2?.currentPeriod || interval.value
      const isHistoryReady = isHistoricalDataReady(pair.value, actualPeriod)
      
      // 优先处理ticker价格更新 - 大幅扩大快速通道覆盖范围
      if (hasTickerUpdate) {
        const tickerData = val1[pair.value]
        // 关键修改：使用实际周期构建key
        const key = `${pair.value}_#_${actualPeriod}`
        
        // 优化：放宽检查条件，优先保证价格数据流动
        // 不再要求历史数据完全就绪或完整K线数据，优先更新价格
        // 优化：扩大快速通道条件，确保价格实时更新
        const shouldUseQuickPath = (
          !isHistoryReady ||           // 历史数据未就绪
          !hasCompleteKlineData ||     // 没有完整K线数据
          Date.now() % 100 < 90 ||     // 90%概率走快速通道，确保流畅更新
          !lastCompleteBar.value[key]  // 没有完整K线缓存时优先更新价格
        )
        
        if (shouldUseQuickPath && subMap[key] && subMap[key].listen && tickerData.last) {
            const resultVal = {
              time: Date.now(),
              close: safeNumber(tickerData.last),
              open: hasCompleteKlineData ? safeNumber(val2.open) : (lastCompleteBar.value[key]?.open || safeNumber(tickerData.last)),
              high: hasCompleteKlineData ? Math.max(safeNumber(val2.high), safeNumber(tickerData.last)) : 
                    Math.max(lastCompleteBar.value[key]?.high || safeNumber(tickerData.last), safeNumber(tickerData.last)),
              low: hasCompleteKlineData ? Math.min(safeNumber(val2.low), safeNumber(tickerData.last)) :
                   Math.min(lastCompleteBar.value[key]?.low || safeNumber(tickerData.last), safeNumber(tickerData.last)),
              volume: hasCompleteKlineData ? safeNumber(val2.volume) : (lastCompleteBar.value[key]?.volume || 0)
            }
            
            // 更新缓存（如果有完整数据的话）
            if (hasCompleteKlineData) {
              lastCompleteBar.value[key] = {
                time: resultVal.time,
                open: safeNumber(val2.open),
                high: safeNumber(val2.high),
                low: safeNumber(val2.low),
                volume: safeNumber(val2.volume)
              }
            }
            
            const currentTime = resultVal.time
            const lastTime = lastBarTimes.get(key) || 0
            if (currentTime > lastTime) {
              subMap[key].listen(resultVal)
              lastBarTimes.set(key, currentTime)
            }
          }
          return
        }
        
        // 超高速价格同步通道：无条件更新最新价格
        // 即使历史数据就绪且有完整K线数据，也要确保价格实时性
        if (subMap[key] && subMap[key].listen && tickerData.last) {
          const ultraFastUpdate = {
            time: Date.now(),
            close: safeNumber(tickerData.last),
            open: lastCompleteBar.value[key]?.open || safeNumber(tickerData.last),
            high: Math.max(lastCompleteBar.value[key]?.high || safeNumber(tickerData.last), safeNumber(tickerData.last)),
            low: Math.min(lastCompleteBar.value[key]?.low || safeNumber(tickerData.last), safeNumber(tickerData.last)),
            volume: lastCompleteBar.value[key]?.volume || 0
          }
          
          // 立即更新，不等待其他条件
          const currentTime = ultraFastUpdate.time
          const lastTime = lastBarTimes.get(key) || 0
          if (currentTime > lastTime) {
            subMap[key].listen(ultraFastUpdate)
            lastBarTimes.set(key, currentTime)
          }
        }
        
        // 如果有ticker、有完整数据、且历史数据就绪，继续正常流程
      }
    }
    // 基本版：只检查周期切换状态（保持原有逻辑）
    else if (isResolutionChanging.value) {
      return
    }
    
    if (handleMonthlyRealtimeUpdate(val1, val2)) {
      return
    }

    if (interval.value === '1M') {
      return
    }

    // 关键修改：优先使用数据中的实际周期，而不是全局的interval.value
    const actualPeriod = val2?.currentPeriod || interval.value
    const key = `${pair.value}_#_${actualPeriod}`
    const last = (val1[pair.value] || {}).last

    if (subMap[key] && last && formatSymbol(subMap[key].symbol) === pair.value) {
      let resultVal

      if (val2 && val2.currentPair && val2.currentPeriod &&
          formatSymbol(subMap[key].symbol) === val2.currentPair &&
          actualPeriod === val2.currentPeriod) {
        resultVal = {
          time: getConsistentTime(val2.time),
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          close: safeNumber(last),
          volume: safeNumber(val2.volume)
        }
        lastCompleteBar.value[key] = {
          time: resultVal.time,
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          volume: safeNumber(val2.volume)
        }
      } else if (val2 && val2.currentPair && formatSymbol(subMap[key].symbol) === val2.currentPair) {
        const baseBar = lastCompleteBar.value[key] || {}
        const currentClose = safeNumber(last)
        resultVal = {
          time: getConsistentTime(val2.time, baseBar.time),
          open: safeNumber(val2.open, baseBar.open || currentClose),
          high: Math.max(safeNumber(val2.high, baseBar.high || currentClose), currentClose),
          low: Math.min(safeNumber(val2.low, baseBar.low || currentClose), currentClose),
          close: currentClose,
          volume: safeNumber(val2.volume, baseBar.volume)
        }
      } else if (lastCompleteBar.value[key]) {
        const baseBar = lastCompleteBar.value[key]
        const currentClose = safeNumber(last)
        resultVal = {
          time: getConsistentTime(null, baseBar.time),
          open: baseBar.open,
          high: Math.max(baseBar.high, currentClose),
          low: Math.min(baseBar.low, currentClose),
          close: currentClose,
          volume: baseBar.volume
        }
      } else {
        const currentClose = safeNumber(last)
        resultVal = {
          time: getConsistentTime(null),
          open: currentClose,
          high: currentClose,
          low: currentClose,
          close: currentClose,
          volume: 0
        }
      }

      const currentUpdate = val1[pair.value] && val1[pair.value]._lastUpdate
      const lastUpdate = lastPriceUpdates.get(pair.value)
      const isPriceUpdate = currentUpdate && currentUpdate !== lastUpdate
      
      // 实时价格更新：确保时间严格递增
      if (isPriceUpdate) {
        lastPriceUpdates.set(pair.value, currentUpdate)
        if (subMap[key] && subMap[key].listen) {
          const currentTime = resultVal.time || Date.now()
          const lastTime = lastBarTimes.get(key) || 0
          // 严格时间验证：只有当新时间大于前一次时间才更新
          if (currentTime > lastTime) {
            subMap[key].listen(resultVal)
            lastBarTimes.set(key, currentTime)
          }
        }
      } else if (subMap[key] && subMap[key].listen) {
        // 对于非实时价格更新，仍然发送更新以保持K线图数据完整性
        const currentTime = resultVal.time || Date.now()
        const lastTime = lastBarTimes.get(key) || 0
        
        // 严格时间递增验证：确保新时间总是大于前一次时间
        if (currentTime > lastTime) {
          subMap[key].listen(resultVal)
          lastBarTimes.set(key, currentTime)
        }
      }
    }
  }, { 
    deep: true, 
    immediate: true,
    flush: 'sync'
  })

  function subscribeBars(symbolInfo: any, resolution: any, onRealtimeCallback: any, subscriberUID: any, onResetCacheNeededCallback: any) {
    const subscriptionKey = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`

    // 彻底清理旧订阅状态
    if (subMap[subscriptionKey]) {
      delete subMap[subscriptionKey]
    }
    if (subMap[subscriberUID]) {
      const oldKey = subMap[subscriberUID]
      if (typeof oldKey === 'string' && subMap[oldKey]) {
        delete subMap[oldKey]
      }
      delete subMap[subscriberUID]
    }

    // 清理月度订阅缓存
    if (resolutionMap[resolution] === '1M') {
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
    }

    // 优化：只清理当前周期相关的状态，保留其他周期的缓存
    if (lastCompleteBar.value[subscriptionKey]) {
      delete lastCompleteBar.value[subscriptionKey]
    }
    lastBarTimes.delete(subscriptionKey)
    
    // 专业版：重置对应symbol的历史数据状态
    if (enableProfessionalOptimization && historicalDataStatus) {
      const key = getHistoricalDataKey(symbolInfo.fullName, resolution)
      historicalDataStatus.delete(key)
    }

    preObj.value = {}
    const symbolPrefix = `${symbolInfo.fullName}-`
    const keysToDelete = []
    dataCache.forEach((value, key) => {
      if (key.startsWith(symbolPrefix)) {
        keysToDelete.push(key)
      }
    })
    keysToDelete.forEach(key => dataCache.delete(key))
    
    const requestKeysToDelete = []
    requestCache.forEach((timestamp, key) => {
      if (key.startsWith(symbolPrefix)) {
        requestKeysToDelete.push(key)
      }
    })
    requestKeysToDelete.forEach(key => requestCache.delete(key))

    const subscription = {
      resolution,
      symbol: symbolInfo.fullName,
      actualResolution: resolutionMap[resolution], // 保存实际周期，用于数据匹配
      resetCallback: onResetCacheNeededCallback,
      listen: (newPriceData) => {
        try {
          // 在周期切换期间阻止实时数据推送
          if (isResolutionChanging && isResolutionChanging.value) {
            return
          }
          onRealtimeCallback(newPriceData)
        } catch (error) {
        }
      }
    }

    subMap[subscriptionKey] = subscription
    subMap[subscriberUID] = subscriptionKey

    if (resolutionMap[resolution] === '1M') {
      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: subscription,
        pair: symbolInfo.fullName
      }
    }
  }

  // 专业版统计报告函数
  const getCacheStats = () => {
    const total = cacheStats.hits + cacheStats.misses
    const hitRate = total > 0 ? Math.round(cacheStats.hits / total * 100) : 0
    return {
      hits: cacheStats.hits,
      misses: cacheStats.misses,
      apiCalls: cacheStats.apiCalls,
      hitRate: `${hitRate}%`,
      uptime: Math.round((Date.now() - cacheStats.lastReset) / 60000) + 'min'
    }
  }


  return {
    subMap, // 导出subMap供外部访问订阅状态
    historyCallback: () => {},
    onReady: (cb: any) => {
      const config = {
        supported_resolutions: [
          '1',
          '3',
          '5',
          '15',
          '30',
          '60',
          '120',
          '240',
          '360',
          '480',
          '720',
          '1D',
          '3D',
          '1W',
          '1M'
        ]
      }
      const timer = setTimeout(() => {
        cb(config)
        clearTimeout(timer)
      }, 0)
    },
    resolveSymbol(symbolName: string, onSymbolResolveCallback: any) {
      let pricescaleValue = pairInfo[symbolName]?.price_scale || 8
      pricescaleValue = pricescaleValue > 16 ? 16 : pricescaleValue
      const symbolInfo = {
        symbol: symbolName.includes('SWAP') ? symbolName.replace('_SWAP', '').replace('_', '') : symbolName.replace('_', '/'),
        name: symbolName,
        ticker: symbolName,
        fullName: symbolName,
        discription: '',
        exchange: 'KTX',
        type: 'Spot',
        has_intraday: true,
        minmov: 1,
        minmove2: 0,
        pricescale: Math.pow(10, pricescaleValue),
        // timezone: 'Etc/UTC', // 时区
        session: '0000-2400:2345671;1',
        volume_precision: 2,
        has_weekly_and_monthly: true,
        has_empty_bars: true
      }
      const timer = setTimeout(() => {
        onSymbolResolveCallback(symbolInfo)
        clearTimeout(timer)
      }, 0)
    },
    getBars,
    subscribeBars,
    unsubscribeBars(subscriberUID: string) {
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }

      const subscriptionKey = subMap[subscriberUID]

      if (subscriptionKey && typeof subscriptionKey === 'string') {
        const subscription = subMap[subscriptionKey]
        if (subscription && subscription.resolution && resolutionMap[subscription.resolution] === '1M') {
          monthlySubscriptionCache = {
            key: null,
            subscription: null,
            pair: null
          }
        }
        delete subMap[subscriptionKey]
      }
      delete subMap[subscriberUID]
    },
    clearCache,
    setForceRefresh,
    cancelAllActiveRequests,
    isResolutionChanging,
    triggerChartReset: (symbol, resolution) => {
      // TradingView官方推荐的重置机制
      const subscriptionKey = `${symbol}_#_${resolutionMap[resolution]}`
      const subscription = subMap[subscriptionKey]
      if (subscription && subscription.resetCallback) {
        subscription.resetCallback()
      }
    }
  }
}