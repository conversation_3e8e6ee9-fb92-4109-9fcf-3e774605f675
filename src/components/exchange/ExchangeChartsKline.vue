<template>
  <div ref="chartsKlineRef" class="exchange-charts-kline-container">
    <div class="charts-kline-subTitle flex-box space-between">
      <BoxXOverflow class="flex-box">
        <span v-if="klineType === 3"></span>
        <template v-else>
          <li
            v-for="(item, index) in timeOptions"
            :key="index"
            class="time-item flex-box">
            <span
              :class="{
                'mg-r12': index !== timeOptions.length - 1,
                active:
                  item.resolution === resolution &&
                  item.chartType === chartType,
              }"
              class="header-btn"
              @click="
                setCurrentResolution({
                  chartType: item.chartType,
                  resolution: item.resolution,
                })
              "
            >
              {{ item.text }}
            </span>
          </li>
          <li :class="{ active: !showPeriod.includes(resolution) || !timeOptions.find((v) => v.resolution === resolution),}" class="period-setting time-item flex-box mg-l12">
            <span v-if="!showPeriod.includes(resolution) || !timeOptions.find((v) => v.resolution === resolution)" class="header-btn">
              {{ currentResolution.text }}
            </span>
            <el-dropdown
              ref="periodDropdownRef"
              trigger="hover"
              @visible-change="editVisibleChange">
              <span class="header-btn cursor-pointer">
                <MonoDownArrowMin size="12" class="fit-tc-primary mg-r8" />
              </span>
              <template #dropdown>
                <el-dropdown-menu style="width:400px;">
                  <div class="pd-tb12 pd-lr24 time-set-dropdown">
                    <div class="flex-box space-between">
                      <span class="font-size-14 tw-5 fit-tc-primary">
                        {{ $t("时间周期") }}
                      </span>

                      <el-button
                        v-if="!isEdit"
                        type="primary"
                        size="small"
                        @click="
                          isEdit = true;
                          editedPeriod = [...showPeriod];
                        "
                      >
                        {{ $t("编辑") }}
                      </el-button>
                      <el-button
                        v-else
                        type="primary"
                        size="small"
                        @click="saveOptions"
                      >
                        {{ $t("保存") }}
                      </el-button>
                    </div>

                    <ul class="flex-box flex-wrap">
                      <li
                        v-for="item in allTimeOptions"
                        :key="item.resolution"
                        :class="{
                          active: showPeriod.includes(item.resolution),
                          checked:
                            (showPeriod.includes(item.resolution) ||
                              editedPeriod.includes(item.resolution)) &&
                            !delPeriod.includes(item.resolution),
                        }"
                        class="exchange-set-time-item"
                        @click="editClick(item)"
                      >
                        {{ item.text }}
                        <div v-if="isEdit" class="icon-my-checked">
                          <MonoRigthChecked size="16" />
                        </div>
                      </li>
                    </ul>
                  </div>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </li>
          <template v-if="klineType === 1">
            <el-tooltip :content="$t('画线工具')" placement="top">
              <li>
                <MonoKlineSetting size="18" class="fit-tc-secondary cursor-pointer mg-r8" @click="isShowDrawLine = !isShowDrawLine" />
              </li>
            </el-tooltip>
            <el-tooltip :content="$t('设置')" placement="top">
              <li>
                <MonoSetting size="18" class="fit-tc-secondary cursor-pointer mg-r8"@click="isShowOriginalSetting = true" />
              </li>
            </el-tooltip>
          </template>
          <template v-else-if="klineType === 2">
            <el-tooltip :content="$t('指标设置')" placement="top">
              <li>
                <MonoIndicator size="18" class="fit-tc-secondary cursor-pointer mg-r8" @click="isShowTechnicalIndicator = true" />
              </li>
            </el-tooltip>
            <el-tooltip :content="$t('设置')" placement="top">
              <li>
                <MonoSetting size="18" class="fit-tc-secondary cursor-pointer mg-r8" @click="isShowTradingViewSetting = true" />
              </li>
            </el-tooltip>
          </template>
        </template>
      </BoxXOverflow>
      <div class="charts-kline-title flex-box">
        <ul v-if="!isMobile" class="flex-box title-nav">
          <li v-for="command in Object.keys(klineObjText)" :key="command" @click="klineType = Number(command)" :class="{'active': klineType === Number(command)}">{{ $t($t(klineObjText[command])) }}</li>
        </ul>
        <el-dropdown v-if="isMobile" @command="(command) => { klineType = command }">
          <span class="fit-tc-primary ts-14 el-dropdown-link flex-box cursor-pointer">
            <span class="mg-r4" style="display:block;white-space:nowrap;">{{ $t(klineObjText[klineType]) }}</span>
            <MonoDownArrowMin size="12"/>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :command="1">{{ $t('基本版') }}</el-dropdown-item>
              <el-dropdown-item :command="2">{{ $t('专业版') }}</el-dropdown-item>
              <el-dropdown-item :command="3">{{ $t('深度') }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-tooltip
          :content="$t(isFullscreen ? '取消全屏' : '全屏')"
          placement="top">
          <div @click="toggleFullscreen">
            <MonoCancelscreen v-if="isFullscreen" size="18" class="fit-tc-secondary cursor-pointer screen-icon-box" />
            <MonoFullscreen v-else size="18" class="fit-tc-secondary cursor-pointer screen-icon-box" />
          </div>
        </el-tooltip>
      </div>
    </div>
    <ExchangeOriginalKline
      v-if="klineType === 1"
      :resolution="resolution"
      :isShowDrawLine="isShowDrawLine"
      :isShowOriginalSetting="isShowOriginalSetting"
      :pair="pair"
      @closeSetting="isShowOriginalSetting = false"
    />
    <ExchangeTradingView
      v-else-if="klineType === 2"
      :pair="pair"
      :resolution="resolution"
      :isLoading="isLoading"
      :isShowTechnicalIndicator="isShowTechnicalIndicator"
      :isShowTradingViewSetting="isShowTradingViewSetting"
      :enableProfessionalOptimization="true"
      @closeTradingViewSetting="closeTradingViewSetting"
      @closeTechnicalIndicator="closeTechnicalIndicator"
      @updateTradingViewData = "updateTradingViewData"
    />
    <ExchangeDepthCharts v-if="klineType === 3 && !isLoading" :depths="depths[pair]" :ticker="ticker" :pair="pair" />
    <!-- <div v-show="isLoading" v-loading="isLoading" class="loadingBox">
    </div> -->
  </div>
</template>
<script lang="ts" setup>
import { setStorage, getStorage } from '@/utils/index'
import {ElPopover, ElCheckboxGroup, ElTooltip, ElDropdown, ElDropdownMenu } from 'element-plus'
import BoxXOverflow from '~/components/common/BoxXOverflow.vue'
import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
import MonoFullscreen from '~/components/common/icon-svg/MonoFullscreen.vue'
import MonoKlineSetting from '~/components/common/icon-svg/MonoKlineSetting.vue'
import MonoIndicator from '~/components/common/icon-svg/MonoIndicator.vue'
import MonoSetting from '~/components/common/icon-svg/MonoSetting.vue'
import MonoCancelscreen from '~/components/common/icon-svg/MonoCancelscreen.vue'
import MonoRigthChecked from '~/components/common/icon-svg/MonoRigthChecked.vue'
import ExchangeTradingView from '~/components/exchange/charts/ExchangeTradingView.vue'
import ExchangeOriginalKline from '~/components/exchange/charts/ExchangeOriginalKline.vue'
import ExchangeDepthCharts from '~/components/exchange/charts/ExchangeDepthCharts.vue'
import screenfull from 'screenfull'
import { commonStore } from '~/stores/commonStore'
import { nextTick } from 'vue'
const store = commonStore()
const { getKlineSocket, cancelKline } = store
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  ticker: {
    type: Object,
    default () {
      return {}
    }
  },
  priceScale: {
    type: [Number, String],
    default: ''
  },
  isCbu: {
    type: Boolean,
    default: false
  },
  savePeriodKey: {
    type: String,
    default: ''
  },
  depths: {
    type: Object,
    default(){
      return {}
    }
  }
})
const klineObjText = {
  1: '基本版',
  2: '专业版',
  3: '深度'
}
const klineType = ref(getStorage('exchange-klineType') || 1)
const { t } = useI18n()
const isLoading = ref(false)
const maOptions  = ref([
  {
    value: 'MA',
    label: 'MA'
  },
  {
    value: 'EMA',
    label: 'EMA'
  },
  {
    value: 'BOLL',
    label: 'BOLL'
  },
  {
    value: 'SAR',
    label: 'SAR'
  },
  {
    value: '',
    label: t('无')
  }
])
const showPeriod = ref([
  '1m',
  '5m',
  '15m',
  '1h',
  '4h',
  '1d',
  '1w'
])
const isEdit = ref(false)
const editedPeriod = ref([])
const delPeriod = ref([])
const resolution = ref((getStorage(props.savePeriodKey || 'exchange-period') || '15m').toString())
const chartType = ref(1)
const allTimeOptions = ref([
  { text: `1${t('分')}`, resolution: '1m', chartType: 1 },
  { text: `5${t('分')}`, resolution: '5m', chartType: 1 },
  { text: `15${t('分')}`, resolution: '15m', chartType: 1 },
  { text: `30${t('分')}`, resolution: '30m', chartType: 1 },
  { text: `1${t('小时')}`, resolution: '1h', chartType: 1 },
  { text: `2${t('小时')}`, resolution: '2h', chartType: 1 },
  { text: `4${t('小时')}`, resolution: '4h', chartType: 1 },
  { text: `6${t('小时')}`, resolution: '6h', chartType: 1 },
  { text: `12${t('小时')}`, resolution: '12h', chartType: 1 },
  { text: `${t('1日')}`, resolution: '1d', chartType: 1 },
  { text: `${t('1周')}`, resolution: '1w', chartType: 1 },
  { text: `${t('1月')}`, resolution: '1M', chartType: 1 }
])
const timeOptions = computed(() => {
  const result = allTimeOptions.value.filter(v => showPeriod.value.includes(v.resolution))
  return result
})
const currentResolution = computed(() => {
  return allTimeOptions.value.concat([{ text: t('分时'), resolution: '1', chartType: 3 }]).find(v => {
    return v.resolution === resolution.value && v.chartType === chartType.value
  }) || {}
})
const isShowTechnicalIndicator = ref(false)
const isShowTradingViewSetting = ref(false)
const closeTechnicalIndicator = () => {
  isShowTechnicalIndicator.value = false
}
const closeTradingViewSetting = () => {
  isShowTradingViewSetting.value = false
}
const editVisibleChange = () => {
  isEdit.value = false
  editedPeriod.value = []
  delPeriod.value = []
}
const editClick = (item) => {
  if (isEdit.value) {
    changePreset(item.resolution, item.chartType)
  } else {
    setCurrentResolution({ chartType: item.chartType, resolution: item.resolution })
  }
}
const changePreset = (period) => {
  const index = editedPeriod.value.indexOf(period)
  if (index > -1) {
    editedPeriod.value.splice(index, 1)
    delPeriod.value.push(period)
  } else {
    const delIndex = delPeriod.value.indexOf(period)
    if (delIndex > -1) {
      delPeriod.value.splice(delIndex, 1)
    }
    editedPeriod.value.push(period)
  }
}
const emit = defineEmits(['changePeriod'])
const setCurrentResolution = (command) => {
  console.log(command, 'dhdhuedhuduheuuhe')
  emit('changePeriod', command.resolution)
  resolution.value = command.resolution
  chartType.value = command.chartType
  setStorage(props.savePeriodKey || 'exchange-period', command.resolution)
  nextTick(() => {
    console.info(periodDropdownRef.value, 'dhdhuedhuduheuuhe')
    periodDropdownRef.value.handleClose()
  })
}

watch(() => klineType.value, (val) => {
  if (val !== 3) {
    setStorage('exchange-klineType', val)
  }
})

watch(() => [resolution.value, klineType.value] , ([newResolution, newKlineType], [oldResolution, oldKlineType]) => {
  console.log('K线类型或周期变化:', { newResolution, newKlineType, oldResolution, oldKlineType })

  if (oldResolution && oldResolution !== newResolution) {
    console.log('周期变化，取消订阅旧周期:', props.pair, oldResolution)
    unSubKline(props.pair, oldResolution)
  }

  // 当klineType从专业版(2)切换到基本版(1)时，需要重新订阅K线数据
  if (oldKlineType && oldKlineType !== newKlineType && newKlineType === 1) {
    console.log('从专业版切换到基本版，重新订阅K线数据:', props.pair, newResolution)
    // 基本版需要重新订阅K线数据
    unSubKline(props.pair, newResolution)
    nextTick(() => {
      subKline(props.pair, newResolution)
    })
  } else {
    nextTick(() => {
      subKline(props.pair, newResolution)
    })
  }
})
watch(() => props.pair, (newVal, oldVal) => {
  unSubKline(oldVal, resolution.value)
  nextTick(() => {
    subKline(newVal, resolution.value)
  })
})
const isFullscreen = ref(false)
const chartsKlineRef = ref(null)
const toggleFullscreen = () => {
  if (!screenfull.isEnabled) {
    return
  }
  if (!screenfull.isFullscreen) {
    screenfull.request(chartsKlineRef.value).then(() => {
      isFullscreen.value = true
    }).catch((err) => {
      console.error('Failed to enable fullscreen', err);
    });
  } else {
    screenfull.exit()
    isFullscreen.value = false;
  }
}
const changeHandler = () => {
  isFullscreen.value = screenfull.isFullscreen
}
const periodDropdownRef = ref(null)
const saveOptions = () => {
  isEdit.value = false
  showPeriod.value = [...new Set(showPeriod.value.concat(editedPeriod.value).filter(v => !delPeriod.value.includes(v)))]
  setStorage('showPeriod', showPeriod.value)
  editedPeriod.value = []
  delPeriod.value = []
  nextTick(() => {
    console.info(periodDropdownRef.value, 'dhduehuehdueue')
    periodDropdownRef.value.handleClose()
  })
}
const subKline = async (pairP, timeP) => {
  isLoading.value = true
  await getKlineSocket(pairP, timeP)
  isLoading.value = false
  return true
}
const unSubKline = (pairP, timeP) => {
  cancelKline(pairP, timeP)
}
const klineData = ref([])
const requestAnimationFrameInterval = ref(null)
const isShowDrawLine = ref(true)
const isShowOriginalSetting = ref(false)
const screenWidth = ref(0)
const isMobile = ref(true)
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
  isMobile.value = screenWidth.value <= 1024
}
const lastShowTime = ref(0)
const onVisibilityChange = () => {
  const isHidden = document.hidden
  const now = Date.now()
  if (!isHidden) {
    // 页面从“隐藏”回到“可见”
    if (now - lastShowTime.value >= 1000 * 60 * 10) {
      // isLoading.value = true
      unSubKline(props.pair, resolution.value)
      nextTick(() => {
        subKline(props.pair, resolution.value)
      })
    }
  }
  // 无论隐藏或显示，都更新时间
  lastShowTime.value = now
}
onMounted(() => {
  document.addEventListener('visibilitychange', onVisibilityChange)
  isLoading.value = true
  const localShowPeriod = getStorage('showPeriod')
  if (localShowPeriod) {
    showPeriod.value = localShowPeriod
  }
  // if (klineType.value === 1) {
  subKline(props.pair, resolution.value)
  // }
  if (screenfull.isEnabled) {
    screenfull.on('change', changeHandler)
  }
  updateScreenWidth()
  window.addEventListener('resize', updateScreenWidth)
})

onUnmounted(() => {
  document.removeEventListener('visibilitychange', onVisibilityChange)
  cancelKline(props.pair, resolution.value)
  screenfull.off('change', changeHandler)
  requestAnimationFrameInterval.value && cancelAnimationFrame(requestAnimationFrameInterval.value)
  window.removeEventListener('resize', updateScreenWidth)
})
</script>
<style lang="scss">
.screen-icon-box{
  margin-left:12px;
  margin-top:8px;
}
.exchange-set-time-item {
  width: 60px;
  margin-right: 12px;
  margin-top: 16px;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  padding: 4px 8px;
  box-sizing: border-box;
  position: relative;
  cursor: pointer;
  @include bg-color(bg-secondary);
  @include color(tc-primary);
  &.active {
    @include bg-color(bg-quaternary);
    @include color(theme);
  }
  &.checked {
    .icon-my-checked {
      @include bg-color(theme);
      @include color(tc-button);
    }
  }
  &:not(.active):hover {
    @include color(theme);
  }
  &:not(.active):active {
    color: #1A57B3;
    [data-theme^="light"] & {
      background: #83868F0F;
    }
    [data-theme^="dark"] & {
      background: #1F2733;
    }
  }
  .icon-my-checked {
    position: absolute;
    right: -8px;
    top: -8px;
    font-size: 16px;
    border-radius: 50%;
    @include bg-color(bg-quaternary);
    @include color(tc-secondary);
  }
  &:nth-of-type(5n) {
    margin-right: 0;
  }
}
.exchange-charts-kline-container{
  position:relative;
  height:calc(100% - 46px);
  @include bg-color(bg-primary);
  .loadingBox {
    width: 100%;
    position: absolute;
    @include bg-color(bg-primary);
    z-index: 999;
    left: 0;
    top: 42px;
    bottom:0;
  }
  .charts-kline-title{
    // position: absolute;
    // top:0;
    // right:0;
    font-size:14px;
    height:44px;
    // padding:0 20px;
    @include color(tc-primary);
    .title-nav{
      li{
        font-size:14px;
        white-space:nowrap;
        cursor:pointer;
        margin-left:12px;
        @include color(tc-secondary);
        &.active{
          @include color(tc-primary);
        }
      }
    }
    .charts-kline-title-right{
      li{
        margin-left:16px;
        cursor:pointer;
        &.active{
          @include color(theme);
        }
      }
    }
  }
  .charts-kline-subTitle{
    height:42px;
    font-size:12px;
    padding:0 10px;
    @include color(tc-primary);
    .time-item{
      cursor:pointer;
      &.active{
        @include color(theme);
      }
      span{
        &.active{
          @include color(theme);
        }
      }
      @include pc-hover{
        &:hover{
          @include color(theme);
        }
      }
      &:active{
        @include color(theme);
      }
    }
  }
}
@include mb {
  .exchange-charts-kline-container{
    height:100%;
  }
}
</style>
