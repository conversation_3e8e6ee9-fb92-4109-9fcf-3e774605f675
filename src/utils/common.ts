
import { imgDmain } from '~/config'
import SocketClass from '~/utils/socket.ts'
export function cloneDeep(data: any) {
  return JSON.parse(JSON.stringify(data))
}
export function imageUrl(url, params) {
  if (!url) {
    return ''
  }
  if (url.startsWith('https://')) {
    return url
  }
  return imgDmain + (!url.startsWith('/') ? ('/' + url) : (url)) + (params ? ('?' + params) : '')
}
export function isValidPwd (pwd) {
  return pwd.length >= 6 && pwd.length <= 20
}
export function isValidSubAccountName(str) {
  return /^(?!^\d+$)[A-Za-z0-9]{8,20}$/u.test(str);
}
export function isValidSubAccountDesc(str) {
  return /^[\w\s\-.,!?@#$%^&*()+=:;"']+$/u.test(str);
}
export function isValidSubAccountPwd (str) {
  return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]).{8,20}$/.test(str)
}
export function isValidEmail (email) {
  // console.info(isEmail, 'isEmailisEmail')
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
}
export function isValidPhone (phoneCode) {
  return /^1\d{10}$/.test(phoneCode)
}
export function isValidFundsPwd (pwd) {
  return /^(?=.*?[a-zA-Z])(?=.*?[0-9]).{6,}$/.test(pwd)
}
export function isApp () {
  if (process.client) {
    const ua = navigator.userAgent.toLowerCase()
    return ua.includes('ktx/ios') || ua.includes('ktx/android')
  }
  return true
}
export function phoneFormat (phoneCode) {
  if (phoneCode && String(phoneCode).length >= 11) {
    return `${phoneCode.slice(0, 3)}****${phoneCode.slice(-4)}`
  } else if (phoneCode && String(phoneCode).length === 10) {
    return `${phoneCode.slice(0, 3)}***${phoneCode.slice(-4)}`
  } else if (phoneCode && String(phoneCode).length === 9) {
    return `${phoneCode.slice(0, 3)}**${phoneCode.slice(-4)}`
  } else if (phoneCode && String(phoneCode).length === 8) {
    return `${phoneCode.slice(0, 3)}*${phoneCode.slice(-4)}`
  } else if (phoneCode && String(phoneCode).length < 8) {
    return `${phoneCode.slice(0, 1)}***${phoneCode.slice(-1)}`
  } else {
    return phoneCode
  }
}
export function emailFormat (emailCode) {
  return `${emailCode.slice(0, 3)}****${emailCode.slice(-3)}`
}
export function valideIdentitynNum (id) {
  // 1 "验证通过!", 0 //校验不通过
  const format = /^(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\d{4}(([1][9]\d{2})|([2]\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\d{3}[0-9xX]$/
  // 号码规则校验
  if (!format.test(id)) {
    // return { 'status': 0, 'msg': '身份证号码不合规' }
    return false
  }
  // 区位码校验
  // 出生年月日校验   前正则限制起始年份为1900;
  const year = id.substr(6, 4) // 身份证年
  const month = id.substr(10, 2) // 身份证月
  const date = id.substr(12, 2) // 身份证日
  const time = Date.parse(month + '-' + date + '-' + year) // 身份证日期时间戳date
  // eslint-disable-next-line camelcase
  const now_time = Date.parse(new Date()) // 当前时间戳
  const dates = (new Date(year, month, 0)).getDate() // 身份证当月天数
  // eslint-disable-next-line camelcase
  if (time > now_time || date > dates) {
    // return { 'status': 0, 'msg': '出生日期不合规' }
    return false
  }
  // 校验码判断
  // eslint-disable-next-line no-array-constructor
  const c = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2) // 系数
  // eslint-disable-next-line no-array-constructor
  const b = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2') // 校验码对照表
  // eslint-disable-next-line camelcase
  const id_array = id.split('')
  let sum = 0
  for (let k = 0; k < 17; k++) {
    sum += parseInt(id_array[k]) * parseInt(c[k])
  }
  // eslint-disable-next-line eqeqeq
  if (id_array[17].toUpperCase() != b[sum % 11].toUpperCase()) {
    // return { 'status': 0, 'msg': '身份证校验码不合规' }
    return false
  }
  // return { 'status': 1, 'msg': '校验通过' }
  return true
}
export function getDomain (hostname = '') {
  if (!hostname && process.server) {
    return '.trade-planets.com'
  }
  try {
    if (!hostname) {
      hostname = location.hostname
    }
    const host = hostname.split(':')[0].split('.')
    return host.length === 2 ? ['', host[0], host[1]].join('.') : ['', host[1], host[2]].join('.')
  } catch (e) {
    return '.trade-planets.com'
  }
}
export function getDomainFirst (hostname = '') {
  if (!hostname && process.server) {
    return 'www'
  }
  try {
    if (!hostname) {
      hostname = location.hostname
    }
    const host = hostname.split(':')[0].split('.')
    return host[0]
  } catch (e) {
    console.info('Utils-index-getDomain', e)
    return 'www'
  }
}
export function timeFormat(time, fmt = 'yyyy.MM.dd hh:mm:ss') { // author: meizz
  // const lang = cookie.getItem('i18nLangssr-k2ex')
  // if (!(lang === 'zh' || lang === 'zh-hk')) {
  //   if (fmt === 'yyyy-MM-dd') {
  //     fmt = 'MM-dd-yyyy'
  //   }
  //   if (fmt === 'yyyy-MM-dd hh:mm:ss') {
  //     fmt = 'MM-dd-yyyy hh:mm:ss'
  //   }
  // }
  if (Number(time)) {
    time = Number(time)
  }
  if (!time) {
    return fmt.replace(/(y+|M+|d+|h+|m+|s+|q+|S+)/ig, '-')
  }
  const date = new Date(time)
  const o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds() // 毫秒
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
    }
  }
  return fmt
}
export const getTimeLeft = function (curLeftTime, isShowDay = true) {
  let d = parseInt(curLeftTime / (24 * 60 * 60 * 1000))
  let h = parseInt(curLeftTime / (60 * 60 * 1000) % 24)
  let m = parseInt(curLeftTime / (60 * 1000) % 60)
  let s = parseInt(curLeftTime / 1000 % 60)
  if (!isShowDay) { // 如果不显示天，需要把天的时间加到小时上
    h += d * 24
  }
  if (s < 0) {
    return false
  }
  d = d < 10 ? '0' + d : d
  h = h < 10 ? '0' + h : h
  m = m < 10 ? '0' + m : m
  s = s < 10 ? '0' + s : s

  return {
    d,
    h,
    m,
    s
  }
}
export function getAttr (paramObject, attr, defaultValue = '') {
  let result = typeof paramObject === 'object' ? JSON.parse(JSON.stringify(paramObject)) : paramObject
  const attrAry = attr.toString().split('.')
  if (((!attr || !paramObject) && attr !== 0 && paramObject !== 0) || attrAry.includes('')) {
    return defaultValue
  }
  if (attrAry.length === 1) {
    return result[attr] || defaultValue
  }
  for (let i = 0; i < attrAry.length; i++) {
    const key = attrAry[i]
    if (!result[key]) {
      result = defaultValue
      break
    }
    result = result[key] || defaultValue
  }
  return result
}
export function imgCompress (file, width, height) {
  /* ********************************************************************
   * file：图片文件
   * width: 压缩后图片宽度
   * height: 压缩后高度
   * 返回一个Promise，参数为处理后的图片
   *********************************************************************/

  return new Promise(function (resolve) {
    try {
      const canvas = document.createElement('canvas')
      const reader = new FileReader()
      const img = new Image()
      reader.readAsDataURL(file)

      reader.onload = function (e) {
        img.src = e.target.result
      }

      img.onload = function () {
        const originWidth = this.width
        const originHeight = this.height
        let targetWidth = originWidth
        let targetHeight = originHeight

        if (originWidth > width) {
          if (originWidth > width || originHeight > height) {
            if (originWidth / originHeight > width / height) {
              targetHeight = height
              targetWidth = Math.round(height * (originWidth / originHeight))
            } else {
              targetWidth = width
              targetHeight = Math.round(width * (originHeight / originWidth))
            }
          }

          const context = canvas.getContext('2d')
          canvas.width = targetWidth
          canvas.height = targetHeight
          context.clearRect(0, 0, targetWidth, targetHeight)
          context.drawImage(img, 0, 0, targetWidth, targetHeight)
          if (canvas.toBlob) {
            canvas.toBlob(function (blob) {
              resolve(blob)
            })
          } else {
            resolve(file)
          }
        } else {
          resolve(file)
        }
      }
    } catch (err) {
      resolve(file)
    }
  })
}
export function getDomainF () {
  if (!process.client) {
    return '.ktx.one'
  }
  const hostname = location.hostname
  const host = hostname.split(':')[0].split('.')
  return host.length === 2 ? ['', host[0], host[1]].join('.') : ['', host[1], host[2]].join('.')
}
// export const socket = new SocketClass(`wss://stream-market.tonetou.com`)
// export const socketAllTicker = new SocketClass(`wss://data.tonetou.com`)
export const marketSocket = new SocketClass(`wss://m-stream${getDomainF()}`)
// export const socketLogin = new SocketClass(`wss://u-stream${getDomainF()}`)
export const tickerSocket = new SocketClass(`wss://stream${getDomainF()}`)
